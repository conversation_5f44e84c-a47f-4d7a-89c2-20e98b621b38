<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Repo\**" />
    <EmbeddedResource Remove="Repo\**" />
    <None Remove="Repo\**" />
  </ItemGroup>
	
	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.12" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.12">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.12" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.12">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
	</ItemGroup>
</Project>
